/** 
 * This script dynamically shows/hides the "Place Order" and "Request for Material" buttons
 * in the project.request one2many form view based on the state field, even before saving.
 */
odoo.define('project_extension.request_button_visibility', function (require) {
    "use strict";
    const FormRenderer = require('web.FormRenderer');
    const { patch } = require('web.utils');

    patch(FormRenderer.prototype, 'project_extension.request_button_visibility', {
        _renderNode(node) {
            const $el = this._super(...arguments);

            // Only patch for project.request one2many form
            if (
                this.state.model === 'project.request' &&
                node.tag === 'button' &&
                (node.attrs.name === 'action_place_order' || node.attrs.name === 'action_request_material')
            ) {
                // Find the state field value in the current record
                const state = this.state.data.state;
                // Show/hide based on state
                if (state !== 'approved') {
                    $el.hide();
                } else {
                    $el.show();
                }
            }
            return $el;
        },
    });
});