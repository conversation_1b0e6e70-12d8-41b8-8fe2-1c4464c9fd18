<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <record id="open_create_project_custom" model="ir.ui.view">
    <field name="name">project.project.form.custom</field>
    <field name="model">project.project</field>
    <field name="arch" type="xml">
      <form string="Project" create="0">
        <field name="stage_id" widget="statusbar"/>
        <sheet>
          <div style="margin-top:32px; width: 100%; display: flex; justify-content: flex-end;">
            <button name="action_open_edit_wizard"
                    string="Edit"
                    type="object"
                    class="btn btn-primary"
                    style="white-space: nowrap;"/>
          </div>
          <group>
            <group>
              <field name="project_custom_id" readonly="1" string="Project ID" class="oe_project_custom_id" placeholder="Auto-generated on save"/>
              <field name="name" string="Project Name" required="1"/>
              <field name="project_poc" string="Project POC" required="1"/>
              <field name="pr_document" widget="many2many_binary" string="PR Document"/>
              <field name="show_place_order_button" invisible="1"/>
              <field name="show_request_material_button" invisible="1"/> 
            </group>
            <group>
              <field name="date_start" string="Start Date" required="1" readonly="1"/>
              <field name="date" string="Due Date" required="1"/>
              <field name="budget" string="Budget"/>
              <field name="total_spent" string="Total Spent"/>
              <field name="budget_remaining" readonly="1"/>
              <field name="enquiry_id" string="Enquiry" options="{'no_create': True}" context="{'form_view_ref': 'enquiry.enquiry_form_view'}"/>
            </group>
          </group>
          <div style="margin-top:32px; width: 100%; display: flex; justify-content: flex-end;">
            <field name="show_move_to_next_stage" invisible="1"/>
            <button name="action_move_to_next_stage"
                    string="Move to Next Stage"
                    type="object"
                    class="btn btn-success"
                    style="white-space: nowrap;"
                    invisible="not show_move_to_next_stage"/>
          </div>
          <notebook>
            <page string="Pre-Sales">
              <div style="width:100%; display:flex; justify-content:flex-end; margin-bottom:8px;">
                <button name="action_open_presales_edit_wizard"
                        string="Edit Pre-Sales"
                        type="object"
                        class="btn btn-primary"
                        context="{'default_project_id': id}"/>
              </div>
              <group>
                <group string="Project Details">
                  <field name="sow_attachment" widget="many2many_binary" string="Scope of Work"/>
                  <field name="project_deadline" string="Project Deadline" readonly="1"/>
                </group>
                <group>
                  <group string="Cost Estimate">
                    <field name="design_time" readonly="1"/>
                    <!-- Design cost -->
                    <label for="design_cost"/>
                    <div class="d-flex align-items-center">
                      <field name="design_quantity" class="me-2" placeholder="Quantity" readonly="1"/>
                      <span class="me-2">×</span>
                      <field name="design_cost" string="Design cost (without GST per quantity)" placeholder="Cost per unit" readonly="1"/>
                      <span class="ms-2">=</span>
                      <field name="design_total" class="ms-2" readonly="1"/>
                    </div>
                    <label for="material_cost"/>
                    <field name="material_cost" readonly="1" nolabel="1"/>
                    <!-- Prototyping cost -->
                    <label for="prototyping_cost"/>
                    <div class="d-flex align-items-center">
                      <field name="prototyping_quantity" class="me-2" placeholder="Quantity" readonly="1"/>
                      <span class="me-2">×</span>
                      <field name="prototyping_cost" string="Prototyping cost (without GST per quantity)" placeholder="Cost per unit" readonly="1"/>
                      <span class="ms-2">=</span>
                      <field name="prototyping_total" class="ms-2" readonly="1"/>
                    </div>
                    <field name="total_cost"/>
                  </group>
                </group>
              </group>
              <div style="width:100%; display:flex; justify-content:flex-end; margin-bottom:8px;">
                <button name="action_manual_sync_presales"
                        type="object"
                        string="Sync Presales Data To Enquiry"
                        class="btn-primary"/>
              </div>
              <field name="custom_line_item_ids" string="Custom Line Items" nolabel="1" readonly="1">
                <tree editable="bottom">
                  <field name="name" string="Service name"/>
                  <field name="quantity" string="Quantity"/>
                  <field name="cost" string="Service cost"/>
                  <field name="notes" string="Description"/>
                </tree>
              </field>
            </page>
            <page string="Payments">
              <group>
                <group string="Payment Status">
                  <field name="payment_status" string="Payment Status" readonly="1"/>
                </group>
              </group>
            </page>
            <page string="Milestones">
              <div style="width:100%; display:flex; justify-content:flex-end; margin-bottom:8px;">
              </div>
              <field name="milestone_ids" context="{'default_project_id': id}">
                <tree editable="bottom">
                  <field name="name" string="Milestone Name"/>
                  <field name="assigned_to"/>
                  <field name="status"/>
                  <field name="date_due"/>
                  <field name="comments"/>
                  <field name="time_taken"/>
                  <field name="mark_as_complete"/>
                  <field name="job_card_ids" string="Job Cards">
                    <tree editable="bottom">
                      <field name="card_name"/>
                    </tree>
                  </field>
                </tree>
                <form>
                  <sheet>
                    <group>
                      <field name="name" string="Milestone Name"/>
                      <field name="date_due" string="Due Date" required="1"/>
                      <field name="assigned_to" string="Assigned To" required="1"/>
                      <field name="status" string="Status" required="1" widget="statusbar"/>
                      <field name="time_taken" string="Time Taken" readonly="1"/>
                      <field name="mark_as_complete" widget="boolean_toggle" string="Mark as Completed"/>
                      <field name="job_card_ids" string="Job Cards">
                        <tree editable="bottom">
                          <field name="card_name"/>
                        </tree>
                      </field>
                    </group>
                  </sheet>
                </form>
              </field>
            </page>
            <page string="Requests" badge="pending_request_count">
              <field name="request_ids">
                <tree editable="bottom">
                  <field name="user_id" string="Requested By" readonly="1" default="context.get('uid')"/>
                  <field name="request_text" string="Request"/>
                  <field name="request_type" string="Request Type"/>
                  <field name="state" string="Status" readonly="1"/>
                  <button name="action_approve"
                          string="Approve"
                          type="object"
                          states="pending"
                          class="btn btn-success"/>
                </tree>
                <form>
                  <group>
                    <field name="user_id" string="Requested By" readonly="1"/>
                    <field name="request_text" string="Request"/>
                    <field name="request_type" string="Request Type"/>
                    <field name="state" string="Status" readonly="1"/>
                    <button name="action_approve"
                            string="Approve"
                            type="object"
                            states="pending"
                            class="btn btn-success"/>
                    <button name="action_place_order"
                            string="Place Order"
                            type="object"
                            states="approved"
                            class="btn btn-primary"/>
                    <button name="action_request_material"
                            string="Request for Material"
                            type="object"
                            states="approved"
                            class="btn btn-secondary"/>        
                  </group>
                </form>
              </field>
            </page>
            <page string="Orders">
              <field name="job_card_ids">
                <tree>
                  <field name="name" string="Part Name"/>
                  <field name="state"/>
                </tree>
              </field>  
            </page>
            <page string="Procurement">
              <field name="procurement_request_ids" widget="many2many_tags" string="Approved Procurement Requests"/>
            </page>
          </notebook>
          <div class="oe_chatter" position="after">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="activity_ids" widget="mail_activity"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </sheet>
      </form>
    </field>
  </record>
</odoo>