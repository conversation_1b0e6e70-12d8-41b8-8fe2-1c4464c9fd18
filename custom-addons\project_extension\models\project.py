from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class ProjectCustomLineItem(models.Model):
    _name = 'project.custom.line.item'
    _description = 'Project Custom Line Item'
    _order ='sequence, id'


    sequence = fields.Integer(string="Sequence", default=10, required=True,index=True)
    project_id = fields.Many2one('project.project', string="Project", ondelete='cascade', required=True)
    name = fields.Char(string="Service name", required=True)
    quantity = fields.Integer(string="Quantity", default=1, required=True)
    cost = fields.Float(string="Service cost", required=True)
    hsn_code = fields.Char(string="HSN/SAC", default='998130')
    notes = fields.Text(string="Description")

class ProjectStageHistory(models.Model):
    _name = 'project.stage.history'
    _description = 'Project Stage History'

    project_id = fields.Many2one('project.project', required=True, ondelete='cascade')
    stage_id = fields.Many2one('project.project.stage', required=True)
    entered_date = fields.Date(required=True, default=fields.Date.today)
    user_id = fields.Many2one('res.users', string="User", default=lambda self: self.env.user, readonly=True)
    days_in_stage = fields.Integer(compute='_compute_current_stage_days', store=True, string="Days in Stage")


    @api.depends('entered_date')
    def _compute_days_in_stage(self):
        today = fields.Date.today()
        for record in self:
            if record.entered_date:
                record.days_in_stage = (today - record.entered_date).days
            else:
                record.days_in_stage = 0


class ProjectProject(models.Model):
    _inherit = 'project.project'

    
    request_ids = fields.One2many('project.request', 'project_id', string="Requests")
    enquiry_id = fields.Many2one('enquiry.enquiry', string="Enquiry", readonly=True, tracking=True)
    sow_attachment = fields.Many2many('ir.attachment', domain="[('res_model','=','project.project')]")
    sow_attachment_filename = fields.Char(string="SOW Filename")
    project_custom_id = fields.Char(string="Project ID", readonly=True, copy=False, tracking=True)
    project_poc = fields.Many2one('hr.employee', string="Project POC", tracking=True, ondelete='set null')
    budget = fields.Monetary(string="Budget", currency_field='currency_id', tracking=True)
    date_start = fields.Date(string="Start Date", default=fields.Date.today(), tracking=True)
    date_due = fields.Date(string="Due Date", tracking=True)
    total_spent = fields.Monetary(string="Total Spent", currency_field='currency_id', tracking=True)
    number_of_orders = fields.Integer(string="Number of Orders", tracking=True)
    project_deadline = fields.Date('Project Deadline', tracking=True, help="Deadline for project completion")
    hsn_code = fields.Char(string="HSN/SAC", default='998130')
    design_time = fields.Float('Design Time (Hours)', tracking=True)
    design_quantity = fields.Integer('Design Quantity', default=1, tracking=True)
    design_cost = fields.Float('Design Cost', tracking=True)
    design_total = fields.Float('Design Total', compute='_compute_subtotals', store=True)
    material_cost = fields.Float('Material Cost', tracking=True)
    prototyping_quantity = fields.Integer('Prototyping Quantity', default=1, tracking=True)
    prototyping_cost = fields.Float('Prototyping Cost', tracking=True)
    prototyping_total = fields.Float('Prototyping Total', compute='_compute_subtotals', store=True)
    prototyping_hsn = fields.Char('HSN/SAC', default='998130', readonly=True)
    custom_line_item_ids = fields.One2many('project.custom.line.item', 'project_id', string='Custom Line Items')
    total_cost = fields.Float('Total Cost', compute='_compute_total_cost', store=True, tracking=True)
    current_stage_days = fields.Integer(string="Days in Current Stage", compute='_compute_current_stage_days', store=False)
    stage_id = fields.Many2one(
        'project.project.stage',
        string="Stage",
        default=lambda self: self.env['project.project.stage'].search([], limit=1).id,
        required=True,
        tracking=True
    )
    budget_remaining = fields.Monetary(
        string="Budget Remaining",
        compute='_compute_budget_remaining',
        currency_field='currency_id',
        store=True
    )

    last_presales_updated = fields.Datetime(
        string="Last Pre-Sales Update Time",
        default=fields.Datetime.now
    )

    
    show_move_to_next_stage = fields.Boolean(
        compute="_compute_show_move_to_next_stage",
        store=False
    )

    payment_status = fields.Selection([
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('po_pending', 'PO Pending Approval'),
        ('po_approved', 'PO Approved'),
        ('bank_pending', 'Bank Transfer Pending Verification'),
        ('bank_verified', 'Bank Transfer Verified')
    ], string='Payment Status', default='pending')


    pending_request_count = fields.Integer(
        string="Pending Requests",
        compute='_compute_pending_request_count'
    )

    order_request_ids = fields.Many2many('project.request','project_order_request_rel','project_id','request_id',string="Order Requests (Approved)")
    procurement_request_ids = fields.Many2many('project.request','project_procurement_request_rel','project_id','request_id',string="Procurement Requests (Approved)")
    show_place_order_button = fields.Boolean(compute='_compute_show_place_order_button', store=False)
    show_request_material_button = fields.Boolean(compute='_compute_show_request_material_button', store=False)
    job_card_ids = fields.One2many('job.card', 'project_id', string="Job Cards")    
    pr_document = fields.Many2many('ir.attachment',string='PR Document',relation='project_pr_document_rel',help="Attachments synced from Enquiry")
    customer_name = fields.Char('Customer Name', tracking=True)
    company_name = fields.Char('Company Name', tracking=True)

    def _compute_show_place_order_button(self):
        for project in self:
            project.show_place_order_button = any(
                req.state == 'approved' and req.request_type == 'order' and not req.processed
                for req in project.request_ids
            )

    def _compute_show_request_material_button(self):
        for project in self:
            project.show_request_material_button = any(
                req.state == 'approved' and req.request_type == 'procurement' and not req.processed
                for req in project.request_ids
            )


    def action_place_order(self):
        self.ensure_one()
        # Find the first unprocessed approved order request
        request = self.request_ids.filtered(lambda r: r.state == 'approved' and r.request_type == 'order' and not r.processed)
        if request:
            request[0].processed = True
        # Open the Job Card form view to create a new job card
        return {
            'type': 'ir.actions.act_window',
            'name': 'Create Job Card',
            'res_model': 'job.card',
            'view_mode': 'form',
            'view_id': self.env.ref('job_card_management.job_card_form_view').id,
            'target': 'current',
            'context': {
                'default_project_id': self.id,
            },
        }

    def action_request_material(self):
        request = self.request_ids.filtered(lambda r: r.state == 'approved' and r.request_type == 'procurement' and not r.processed)
        if request:
            request[0].processed = True
        # Your logic for requesting material

    def _compute_pending_request_count(self):
        for project in self:
            project.pending_request_count = len(
                project.request_ids.filtered(lambda r: r.state == 'pending')
            )
    @api.depends('design_cost', 'design_quantity', 'material_cost', 'prototyping_cost', 'prototyping_quantity', 'custom_line_item_ids.cost', 'custom_line_item_ids.quantity')
    def _compute_subtotals(self):
        for record in self:
            record.design_total = record.design_cost * record.design_quantity
            record.prototyping_total = record.prototyping_cost * record.prototyping_quantity

    @api.depends('design_total', 'material_cost', 'prototyping_total', 'custom_line_item_ids.cost', 'custom_line_item_ids.quantity')
    def _compute_total_cost(self):
        for record in self:
            standard_costs = record.design_total + record.material_cost + record.prototyping_total
            custom_costs = sum(item.cost * item.quantity for item in record.custom_line_item_ids)
            record.total_cost = standard_costs + custom_costs

    def _prepare_invoice_lines(self):
        """Prepare line items for invoice creation"""
        invoice_lines = []

        # Add design cost if present
        if self.design_cost and self.design_quantity:
            invoice_lines.append((0, 0, {
                'name': 'Design Services',
                'quantity': self.design_quantity,
                'price_unit': self.design_cost,
                'hsn_code': self.hsn_code,
            }))

        # Add material cost if present
        if self.material_cost:
            invoice_lines.append((0, 0, {
                'name': 'Materials',
                'quantity': 1.0,
                'price_unit': self.material_cost,
                'hsn_code': '998130',
            }))

        # Add prototyping cost if present
        if self.prototyping_cost and self.prototyping_quantity:
            invoice_lines.append((0, 0, {
                'name': 'Prototyping Services',
                'quantity': self.prototyping_quantity,
                'price_unit': self.prototyping_cost,
                'hsn_code': self.prototyping_hsn,
            }))

        # Add custom line items if present
        for item in self.custom_line_item_ids:
            invoice_lines.append((0, 0, {
                'name': item.name,
                'quantity': item.quantity,
                'price_unit': item.cost,
                'hsn_code': item.hsn_code,
            }))
        return invoice_lines

    def _calculate_expected_invoice_total(self):
        """Calculate what the total would be for a new invoice based on current costs"""
        total = 0.0

        # Design costs
        if self.design_cost and self.design_quantity:
            total += self.design_cost * self.design_quantity

        # Material costs
        if self.material_cost:
            total += self.material_cost

        # Prototyping costs
        if self.prototyping_cost and self.prototyping_quantity:
            total += self.prototyping_cost * self.prototyping_quantity

        # Custom line items
        for item in self.custom_line_item_ids:
            total += item.cost * item.quantity

        # Add taxes (same 18% calculation as in ProformaInvoice._compute_amounts)
        total_with_tax = total * 1.18

        return total_with_tax
                
    @api.depends('stage_id')
    def _compute_current_stage_days(self):
        for project in self:
            days = 0
            if project.stage_id:
                history = self.env['project.stage.history'].search(
                    [('project_id', '=', project.id), ('stage_id', '=', project.stage_id.id)],
                    order='entered_date desc', limit=1
                )
                if history and history.entered_date:
                    today = fields.Date.today()
                    entered = history.entered_date
                    if isinstance(entered, str):
                        entered = fields.Date.from_string(entered)
                    days = (today - entered).days
            project.current_stage_days = days


   
    @api.model  
    def _get_next_project_custom_id(self):
        return self.env['ir.sequence'].next_by_code('project.project.custom.id') or '/'
    
    @api.model
    def create(self, vals):
        if not vals.get('project_custom_id'):
            vals['project_custom_id'] = self._get_next_project_custom_id()
        return super().create(vals)


    @api.depends('budget', 'total_spent')
    def _compute_budget_remaining(self):
        for project in self:
            project.budget_remaining = project.budget - project.total_spent

    @api.constrains('budget', 'total_spent')
    def _check_budget_values(self):
        for project in self:
            if project.budget < 0:
                raise UserError(_("Budget cannot be negative"))
            if project.total_spent < 0:
                raise UserError(_('Total spent cannot be negative'))
            if project.budget_remaining < 0:
                _logger.warning(f"Project {project.name} has exceeded its budget!")

    @api.model
    def update_project_totals(self):
        "method to update project totals from related orders"
        projects = self.search([])
        for project in projects:
            orders = self.env['purchase.order'].sudo().search([('project_id', '=', project.id)])
            project.update({
                'number_of_orders': len(orders),
                'total_spent': sum(order.amount_total for order in orders)
            })
            _logger.info('Updated project totals for %s', project.name)

    def _default_stage_id(self):
        stage = self.env['project.stage'].search([], limit=1)
        if not stage:
            raise UserError(_('No project stage found. Please create at least one project stage.'))
        return stage.id

    milestone_ids = fields.One2many(
        'project.milestone', 'project_id', string="Milestones")

    def action_open_edit_wizard(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Edit Project',
            'res_model': 'project.edit.wizard',
            'view_mode': 'form',
            'view_id': self.env.ref('project_extension.view_project_edit_wizard_form').id,
            'target': 'new',
            'context': {
                'default_project_id': self.id,
                'active_id': self.id,
                'active_model': 'project.project',
            },
        }

    def action_move_to_next_stage(self):
        self.ensure_one()
        incomplete_milestones = self.milestone_ids.filtered(lambda m: not m.mark_as_complete)
        if incomplete_milestones:
            raise UserError(_("You cannot move stages until all milestones are marked as completed by the project manager"))

        stages = self.env['project.project.stage'].search([])
        if not stages:
            raise UserError(_("No project stages found"))

        current_index = stages.ids.index(self.stage_id.id) if self.stage_id.id in stages.ids else -1
        if current_index == -1:
            raise UserError(_("Current stage not found in available stages"))

        # If not last stage, move to next
        if current_index < len(stages) - 1:
            next_stage = stages[current_index + 1]
            # If moving to the final stage, show confirmation
            if current_index + 1 == len(stages) - 1:
                return {
                    'type': 'ir.actions.act_window',
                    'name': _('Confirm Completion'),
                    'res_model': 'project.complete.confirm.wizard',
                    'view_mode': 'form',
                    'target': 'new',
                    'context': {'default_project_id': self.id, 'default_next_stage_id': next_stage.id},
                }
            self.write({'stage_id': next_stage.id})
            self.env['project.stage.history'].create({
                'project_id': self.id,
                'stage_id': next_stage.id,
                'entered_date': fields.Date.today()
            })
            return {
                'effect': {
                    'fadeout': 'slow',
                    'message': 'Project moved to next stage',
                    'type': 'rainbow_man',
                }
            }
        else:
            raise UserError(_("Project is already in the final stage"))

    def action_open_presales_edit_wizard(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Edit Pre-Sales',
            'res_model': 'project.presales.edit.wizard',
            'view_mode': 'form',
            'view_id': self.env.ref('project_extension.view_project_presales_edit_wizard').id,
            'target': 'new',
            'context': {
                'active_id': self.id,
            },
        }

    def project_update_all_action(self):
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'project.project',
            'view_mode': 'form',
            'res_id': self.id,
            'view_id': self.env.ref('project_extension.open_create_project_custom').id,
            'target': 'current',
        }

    def action_manual_sync_presales(self):
        self.ensure_one()
        if not self.enquiry_id:
            raise UserError("No enquiry linked to this project.")
        # Remove old enquiry line items if needed
        self.enquiry_id.custom_line_item_ids.unlink()
        # Copy each project line item to enquiry
        for line in self.custom_line_item_ids:
            self.env['enquiry.custom.line.item'].create({
                'enquiry_id': self.enquiry_id.id,
                'name': line.name,
                'quantity': line.quantity,
                'cost': line.cost,
                'notes': line.notes,
                'hsn_code': line.hsn_code,
            })
        return True

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        res = super().fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)

        if view_type == 'form' and self.env.context.get('params', {}).get('action') and not self.env.context.get('default_name'):
            # In edit/view mode — make all fields readonly
            from lxml import etree
            doc = etree.XML(res['arch'])
            for node in doc.xpath("//field"):
                node.set('readonly', '1')
                modifiers = eval(node.get("modifiers", "{}"))
                modifiers['readonly'] = True
                node.set("modifiers", str(modifiers))
            res['arch'] = etree.tostring(doc, encoding='unicode')

        return res
    
    def _compute_show_move_to_next_stage(self):
        for project in self:
            # Show button only if current stage's name is "Active"
            project.show_move_to_next_stage = project.stage_id and project.stage_id.name == "Active"

    def write(self, vals):
        # Detect stage change for history tracking
        stage_changed = 'stage_id' in vals

        # Track presales field updates
        presales_fields = [
            'design_cost', 'material_cost', 'prototyping_cost',
            'design_quantity', 'prototyping_quantity',
            'custom_line_item_ids', 'sow_attachment'
        ]
        if any(field in vals for field in presales_fields):
            vals['last_presales_updated'] = fields.Datetime.now()

        stage_before = {proj.id: proj.stage_id.id for proj in self} if stage_changed else {}
        
        result = super().write(vals)

        # After write: log stage history if it changed
        if stage_changed:
            for project in self:
                old_stage_id = stage_before.get(project.id)
                new_stage_id = project.stage_id.id
                if new_stage_id and old_stage_id != new_stage_id:
                    self.env['project.stage.history'].create({
                        'project_id': project.id,
                        'stage_id': new_stage_id,
                        'entered_date': fields.Date.today()
                    })

        # Sync presales if needed
        if not self.env.context.get('syncing_presales'):
            self.sync_presales_from_project()

        return result

    def sync_presales_from_project(self):
        for project in self:
            enquiry = project.enquiry_id
            if not enquiry:
                continue
            if project.last_presales_updated and (
                not enquiry.last_presales_updated or project.last_presales_updated > enquiry.last_presales_updated
            ):
                
                _logger.info(f"[SYNC] Syncing from Project {project.id} to Enquiry {enquiry.id}")
                _logger.info(f"[SYNC] Project Timestamp: {project.last_presales_updated} | Enquiry Timestamp: {enquiry.last_presales_updated}")

                enquiry.with_context(syncing_presales=True).write({
                    'design_cost': project.design_cost,
                    'design_time': project.design_time,
                    'material_cost': project.material_cost,
                    'prototyping_cost': project.prototyping_cost,
                    'design_quantity': project.design_quantity,
                    'prototyping_quantity': project.prototyping_quantity,
                    'project_deadline' : project.project_deadline,
                    'last_presales_updated': project.last_presales_updated,
                    'sow_attachment_ids': [(6, 0, project.sow_attachment.ids)],
                })

                enquiry.custom_line_item_ids.unlink()
                for line in project.custom_line_item_ids:
                    self.env['enquiry.custom.line.item'].create({
                        'enquiry_id': enquiry.id,
                        'name': line.name,
                        'quantity': line.quantity,
                        'cost': line.cost,
                        'hsn_code': line.hsn_code,
                        'notes': line.notes
                    })

                enquiry._compute_total_cost()
            
class ProjectMilestone(models.Model):
    _name = 'project.milestone'
    _description = 'Project Milestone'

    dependency_ids = fields.Many2many(
        'project.milestone',
        'milestone_dependencies_rel',
        'parent_id', 'child_id',
        string="Dependencies"
    )

    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Medium'),
        ('2', 'High'),
        ('3', 'Critical')
    ], default='1')
    
    date_start = fields.Date(string="Start Date")
    date_end = fields.Date(string="End Date")
    job_card_ids = fields.One2many('job.card', 'milestone_id', string="Subtasks / Job Cards")
    mark_as_complete = fields.Boolean(string="Mark as Complete")
    name = fields.Char(string="Milestone Name", tracking=True)
    project_id = fields.Many2one('project.project', string="Project", ondelete='cascade', required=True, tracking=True)
    task_name = fields.Char(string="Task To Finish", tracking=True)
    assigned_to = fields.Many2one('res.users', string="Assigned To", tracking=True)
    status = fields.Selection([
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ], string="Status", default='pending', tracking=True)
    date_due = fields.Date(string="Due Date", tracking=True)
    comments = fields.Text(string="Comments", tracking=True)

    is_reached = fields.Boolean(
        string="Reached",
        compute="_compute_is_reached",
        store=True,
        help="Indicates if the milestone is reached (completed)."
    )
    deadline = fields.Date(
        string="Deadline",
        compute="_compute_deadline",
        store=True,
        help="Alias for date_due to support legacy code."
    )
    time_taken = fields.Char(
        string="Time Taken",
        compute="_compute_time_taken",
        store=True,
        help="Time taken from 'In Progress' to 'Completed' in days, hours, minutes, and seconds."
    )
    
    date_in_progress = fields.Datetime(
    string="Date In Progress",
    help="Datetime when the milestone status was set to 'In Progress'."
    )

    date_completed = fields.Datetime(
    string="Date Completed",
    help="Datetime when the milestone was marked as completed."
    )

    status_locked = fields.Selection([
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ], string='Locked Status', default='', store=True)

    @api.depends('status')
    def _compute_is_reached(self):
        for milestone in self:
            milestone.is_reached = milestone.status == 'completed'

    @api.depends('date_due')
    def _compute_deadline(self):
        for milestone in self:
            milestone.deadline = milestone.date_due

    @api.onchange('status')
    def _onchange_status_set_date_in_progress(self):
        for milestone in self:
            if milestone.status == 'in_progress' and not milestone.date_in_progress:
                milestone.date_in_progress = fields.Datetime.now()

    @api.depends('date_in_progress', 'date_completed')
    def _compute_time_taken(self):
        for milestone in self:
            if milestone.status == 'completed' and milestone.date_in_progress and milestone.date_completed:
                dt_start = milestone.date_in_progress
                dt_end = milestone.date_completed
                if isinstance(dt_start, str):
                    dt_start = fields.Datetime.from_string(dt_start)
                if isinstance(dt_end, str):
                    dt_end = fields.Datetime.from_string(dt_end)
                delta = dt_end - dt_start
                days = delta.days
                seconds = delta.seconds
                hours = seconds // 3600
                minutes = (seconds % 3600) // 60
                secs = seconds % 60
                milestone.time_taken = f"{days}d {hours}h {minutes}m {secs}s"
            else:
                milestone.time_taken = "0d 0h 0m 0s"

    @api.onchange('status')
    def _onchange_status_lock_progress(self):
        for milestone in self:
            if not milestone._origin:
                continue
            previous_status = milestone._origin.status
            if previous_status == 'pending' and milestone.status == 'in_progress':
                milestone.status_locked = 'in_progress'
            elif previous_status == 'in_progress' and milestone.status == 'completed':
                milestone.status_locked = 'completed'
            elif previous_status == 'in_progress' and milestone.status == 'pending':
                milestone.status = 'in_progress'
            elif previous_status == 'completed' and milestone.status != 'completed':
                milestone.status = 'completed'

    def write(self, vals):
        for rec in self:
            if vals.get('status') == 'in_progress' and not rec.date_in_progress:
                vals['date_in_progress'] = fields.Datetime.now()
            if vals.get('status') == 'completed':
                if not rec.date_completed:
                    vals['date_completed'] = fields.Datetime.now()
                if not rec.date_in_progress:
                    vals['date_in_progress'] = vals['date_completed']
        return super().write(vals)
    
    # Enhanced completion logic
    def action_mark_complete(self):
        for milestone in self:
            if any(not dep.is_reached for dep in milestone.dependency_ids):
                raise UserError(_("Cannot complete milestone with unfinished dependencies"))
            vals = {
                'status': 'completed',
                'date_completed': fields.Datetime.now()
        }
        if not milestone.date_in_progress:
            vals['date_in_progress'] = vals['date_completed']
        milestone.write(vals)


class ProjectRequest(models.Model):
    _name = 'project.request'
    _description = 'Project Request'

    project_id = fields.Many2one('project.project', string="Project", required=True, ondelete='cascade')
    user_id = fields.Many2one('res.users', string="Requested By", default=lambda self: self.env.user, required=True)
    request_type= fields.Selection([
        ('order', 'Order'),
        ('procurement', 'Procurement'),], string="Request Type", required=True)
    request_text = fields.Char(string="Request", required=True)
    state = fields.Selection([
        ('pending', 'Pending'),
        ('approved', 'Approved'),
    ], default='pending', string="Status")

    processed = fields.Boolean(string="Processed", default=False)
    
    def action_approve(self):
        for rec in self:
            rec.state = 'approved'
            if rec.request_type == 'order':
                rec.project_id.order_request_ids = [(4, rec.id)]
            elif rec.request_type == 'procurement':
                rec.project_id.procurement_request_ids = [(4, rec.id)]
    
    def action_place_order(self):
        self.ensure_one()
        # Find the first unprocessed approved order request
        request = self.request_ids.filtered(lambda r: r.state == 'approved' and r.request_type == 'order' and not r.processed)
        if request:
            request[0].processed = True
        # Open the Job Card form view to create a new job card
        return {
            'type': 'ir.actions.act_window',
            'name': 'Create Job Card',
            'res_model': 'job.card',
            'view_mode': 'form',
            'view_id': self.env.ref('job_card_management.job_card_form_view').id,
            'target': 'current',
            'context': {
                'default_project_id': self.id,
            },
        }

    def action_request_material(self):
        request = self.request_ids.filtered(lambda r: r.state == 'approved' and r.request_type == 'procurement' and not r.processed)
        if request:
            request[0].processed = True